<?php
/* Smarty version 3.1.48, created on 2025-07-21 02:13:01
  from 'C:\xampp\htdocs\templates\orderforms\widdx_modern\products.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_687d860dc90e03_23577441',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '22fb781f33e15927f87512f20cfb792079b9f8db' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\orderforms\\widdx_modern\\products.tpl',
      1 => 1753047887,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:orderforms/widdx_modern/common.tpl' => 1,
    'file:orderforms/widdx_modern/sidebar-categories.tpl' => 1,
  ),
),false)) {
function content_687d860dc90e03_23577441 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:orderforms/widdx_modern/common.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div class="widdx-order-form">
    <!-- Progress Indicator -->
    <div class="widdx-progress-container mb-4">
        <div class="widdx-progress-steps">
            <div class="widdx-step active" data-step="1">
                <div class="widdx-step-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="widdx-step-label">Choose Products</div>
            </div>
            <div class="widdx-step" data-step="2">
                <div class="widdx-step-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="widdx-step-label">Configure</div>
            </div>
            <div class="widdx-step" data-step="3">
                <div class="widdx-step-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="widdx-step-label">Payment</div>
            </div>
            <div class="widdx-step" data-step="4">
                <div class="widdx-step-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="widdx-step-label">Complete</div>
            </div>
        </div>
        <div class="widdx-progress-bar">
            <div class="widdx-progress-fill" style="width: 25%"></div>
        </div>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3 col-md-4">
                <div class="widdx-sidebar">
                    <?php $_smarty_tpl->_subTemplateRender("file:orderforms/widdx_modern/sidebar-categories.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9 col-md-8">
                <div class="widdx-main-content">
                    <!-- Hero Section -->
                    <div class="widdx-hero-section mb-5">
                        <div class="widdx-hero-content">
                            <h1 class="widdx-hero-title">Choose Your Perfect Hosting Solution</h1>
                            <p class="widdx-hero-subtitle">Professional hosting services with modern payment processing and 24/7 support</p>
                        </div>
                        <div class="widdx-hero-graphic">
                            <i class="fas fa-server fa-4x"></i>
                        </div>
                    </div>

                    <!-- Product Groups -->
                    <?php if ($_smarty_tpl->tpl_vars['productGroups']->value) {?>
                        <div class="widdx-product-groups">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['productGroups']->value, 'productGroup');
$_smarty_tpl->tpl_vars['productGroup']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['productGroup']->value) {
$_smarty_tpl->tpl_vars['productGroup']->do_else = false;
?>
                                <div class="widdx-product-group mb-5" id="group-<?php echo $_smarty_tpl->tpl_vars['productGroup']->value->id;?>
">
                                    <div class="widdx-group-header">
                                        <h2 class="widdx-group-title"><?php echo $_smarty_tpl->tpl_vars['productGroup']->value->name;?>
</h2>
                                        <?php if ($_smarty_tpl->tpl_vars['productGroup']->value->tagline) {?>
                                            <p class="widdx-group-tagline"><?php echo $_smarty_tpl->tpl_vars['productGroup']->value->tagline;?>
</p>
                                        <?php }?>
                                    </div>

                                    <div class="widdx-products-grid">
                                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['productGroup']->value->products, 'product');
$_smarty_tpl->tpl_vars['product']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['product']->value) {
$_smarty_tpl->tpl_vars['product']->do_else = false;
?>
                                            <div class="widdx-product-card" data-product-id="<?php echo $_smarty_tpl->tpl_vars['product']->value->id;?>
">
                                                <div class="widdx-product-header">
                                                    <?php if ($_smarty_tpl->tpl_vars['product']->value->featureHighlight) {?>
                                                        <div class="widdx-product-badge">
                                                            <?php echo $_smarty_tpl->tpl_vars['product']->value->featureHighlight;?>

                                                        </div>
                                                    <?php }?>
                                                    <h3 class="widdx-product-name"><?php echo $_smarty_tpl->tpl_vars['product']->value->name;?>
</h3>
                                                    <?php if ($_smarty_tpl->tpl_vars['product']->value->tagLine) {?>
                                                        <p class="widdx-product-tagline"><?php echo $_smarty_tpl->tpl_vars['product']->value->tagLine;?>
</p>
                                                    <?php }?>
                                                </div>

                                                <div class="widdx-product-pricing">
                                                    <?php if ($_smarty_tpl->tpl_vars['product']->value->bid) {?>
                                                        <div class="widdx-price-container">
                                                            <span class="widdx-price-amount"><?php echo $_smarty_tpl->tpl_vars['product']->value->pricing->minPrice->price;?>
</span>
                                                            <span class="widdx-price-cycle">/<?php echo $_smarty_tpl->tpl_vars['product']->value->pricing->minPrice->cycle;?>
</span>
                                                        </div>
                                                        <?php if ($_smarty_tpl->tpl_vars['product']->value->pricing->minPrice->setupFee->toNumeric() > 0) {?>
                                                            <div class="widdx-setup-fee">
                                                                Setup: <?php echo $_smarty_tpl->tpl_vars['product']->value->pricing->minPrice->setupFee;?>

                                                            </div>
                                                        <?php }?>
                                                    <?php } else { ?>
                                                        <div class="widdx-price-container">
                                                            <span class="widdx-price-amount">Free</span>
                                                        </div>
                                                    <?php }?>
                                                </div>

                                                <div class="widdx-product-features">
                                                    <?php if ($_smarty_tpl->tpl_vars['product']->value->features) {?>
                                                        <ul class="widdx-feature-list">
                                                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['product']->value->features, 'feature');
$_smarty_tpl->tpl_vars['feature']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['feature']->value) {
$_smarty_tpl->tpl_vars['feature']->do_else = false;
?>
                                                                <li class="widdx-feature-item">
                                                                    <i class="fas fa-check text-success"></i>
                                                                    <?php echo $_smarty_tpl->tpl_vars['feature']->value->feature;?>

                                                                </li>
                                                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                                        </ul>
                                                    <?php }?>
                                                </div>

                                                <div class="widdx-product-actions">
                                                    <?php if ($_smarty_tpl->tpl_vars['product']->value->isFree) {?>
                                                        <a href="<?php echo $_smarty_tpl->tpl_vars['product']->value->productUrl;?>
" class="btn btn-widdx-primary btn-block">
                                                            <i class="fas fa-download"></i>
                                                            Get Started Free
                                                        </a>
                                                    <?php } else { ?>
                                                        <a href="<?php echo $_smarty_tpl->tpl_vars['product']->value->productUrl;?>
" class="btn btn-widdx-primary btn-block">
                                                            <i class="fas fa-shopping-cart"></i>
                                                            Order Now
                                                        </a>
                                                    <?php }?>
                                                    <?php if ($_smarty_tpl->tpl_vars['product']->value->description) {?>
                                                        <button type="button" class="btn btn-outline-secondary btn-sm mt-2" data-toggle="modal" data-target="#product-details-<?php echo $_smarty_tpl->tpl_vars['product']->value->id;?>
">
                                                            <i class="fas fa-info-circle"></i>
                                                            More Details
                                                        </button>
                                                    <?php }?>
                                                </div>
                                            </div>

                                            <!-- Product Details Modal -->
                                            <?php if ($_smarty_tpl->tpl_vars['product']->value->description) {?>
                                                <div class="modal fade" id="product-details-<?php echo $_smarty_tpl->tpl_vars['product']->value->id;?>
" tabindex="-1" role="dialog" aria-labelledby="product-details-<?php echo $_smarty_tpl->tpl_vars['product']->value->id;?>
-title" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="product-details-<?php echo $_smarty_tpl->tpl_vars['product']->value->id;?>
-title"><?php echo $_smarty_tpl->tpl_vars['product']->value->name;?>
</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <?php echo $_smarty_tpl->tpl_vars['product']->value->description;?>

                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                                                <a href="<?php echo $_smarty_tpl->tpl_vars['product']->value->productUrl;?>
" class="btn btn-widdx-primary">Order Now</a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php }?>
                                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                    </div>
                                </div>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </div>
                    <?php }?>

                    <!-- Domain Registration Section -->
                    <?php if ($_smarty_tpl->tpl_vars['registerdomainenabled']->value || $_smarty_tpl->tpl_vars['transferdomainenabled']->value) {?>
                        <div class="widdx-domain-section mt-5">
                            <div class="widdx-section-header text-center mb-4">
                                <h2 class="widdx-section-title">Domain Services</h2>
                                <p class="widdx-section-subtitle">Secure your perfect domain name</p>
                            </div>

                            <div class="row">
                                <?php if ($_smarty_tpl->tpl_vars['registerdomainenabled']->value) {?>
                                    <div class="col-md-6 mb-4">
                                        <div class="widdx-domain-card">
                                            <div class="widdx-domain-icon">
                                                <i class="fas fa-globe fa-3x"></i>
                                            </div>
                                            <h3>Register Domain</h3>
                                            <p>Find and register your perfect domain name</p>
                                            <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=add&domain=register" class="btn btn-success btn-block">
                                                <i class="fas fa-search"></i>
                                                Search Domains
                                            </a>
                                        </div>
                                    </div>
                                <?php }?>
                                <?php if ($_smarty_tpl->tpl_vars['transferdomainenabled']->value) {?>
                                    <div class="col-md-6 mb-4">
                                        <div class="widdx-domain-card">
                                            <div class="widdx-domain-icon">
                                                <i class="fas fa-exchange-alt fa-3x"></i>
                                            </div>
                                            <h3>Transfer Domain</h3>
                                            <p>Transfer your existing domain to us</p>
                                            <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=add&domain=transfer" class="btn btn-info btn-block">
                                                <i class="fas fa-arrow-right"></i>
                                                Transfer Domain
                                            </a>
                                        </div>
                                    </div>
                                <?php }?>
                            </div>
                        </div>
                    <?php }?>

                    <!-- Trust Indicators -->
                    <div class="widdx-trust-section mt-5">
                        <div class="widdx-trust-indicators">
                            <div class="widdx-trust-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Secure Payments</span>
                            </div>
                            <div class="widdx-trust-item">
                                <i class="fas fa-headset"></i>
                                <span>24/7 Support</span>
                            </div>
                            <div class="widdx-trust-item">
                                <i class="fas fa-rocket"></i>
                                <span>Fast Setup</span>
                            </div>
                            <div class="widdx-trust-item">
                                <i class="fas fa-undo"></i>
                                <span>30-Day Guarantee</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include order form specific JavaScript -->
<?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/orderforms/widdx_modern/js/order-form.js"><?php echo '</script'; ?>
>
<?php }
}
