<?php
/* Smarty version 3.1.48, created on 2025-07-21 02:13:02
  from 'C:\xampp\htdocs\templates\orderforms\widdx_modern\common.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_687d860e4102f4_10340145',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'b327c84cd7bcd95831375f93f9a04d666a846018' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\orderforms\\widdx_modern\\common.tpl',
      1 => 1753047917,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_687d860e4102f4_10340145 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="theme-color" content="#2c5aa0">
    <title><?php if ($_smarty_tpl->tpl_vars['kbarticle']->value['title']) {
echo $_smarty_tpl->tpl_vars['kbarticle']->value['title'];?>
 - <?php }
echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
 - <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
</title>
    
    <!-- WIDDX Order Form Styles -->
    <link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/orderforms/widdx_modern/css/order-form.css" rel="stylesheet">
    <link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/WIDDX/css/theme.css" rel="stylesheet">
    <link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/WIDDX/css/accessibility.css" rel="stylesheet">
    
    <!-- Bootstrap and FontAwesome -->
    <link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/assets/css/fontawesome-all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- WHMCS Variables -->
    <?php echo '<script'; ?>
>
        var csrfToken = '<?php echo $_smarty_tpl->tpl_vars['token']->value;?>
',
            whmcsBaseUrl = '<?php echo $_smarty_tpl->tpl_vars['systemurl']->value;?>
',
            orderFormTemplate = 'widdx_modern';
    <?php echo '</script'; ?>
>
    
    <!-- jQuery and Bootstrap JS -->
    <?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/assets/js/jquery.min.js"><?php echo '</script'; ?>
>
    <?php echo '<script'; ?>
 src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/assets/js/bootstrap.bundle.min.js"><?php echo '</script'; ?>
>
    
    <?php echo $_smarty_tpl->tpl_vars['headoutput']->value;?>

</head>

<body class="widdx-order-form-body">
    <?php echo $_smarty_tpl->tpl_vars['headeroutput']->value;?>

    
    <!-- Skip Links for Accessibility -->
    <div class="widdx-skip-links">
        <a href="#main-content" class="widdx-skip-link">Skip to main content</a>
        <a href="#order-form" class="widdx-skip-link">Skip to order form</a>
    </div>
    
    <!-- Header -->
    <header class="widdx-order-header" role="banner">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="widdx-logo-container">
                        <?php if ($_smarty_tpl->tpl_vars['logo']->value) {?>
                            <img src="<?php echo $_smarty_tpl->tpl_vars['logo']->value;?>
" alt="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
" class="widdx-logo">
                        <?php } else { ?>
                            <h1 class="widdx-company-name"><?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
</h1>
                        <?php }?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="widdx-header-actions text-right">
                        <?php if ($_smarty_tpl->tpl_vars['loggedin']->value) {?>
                            <div class="widdx-user-info">
                                <span class="widdx-welcome">Welcome, <?php echo $_smarty_tpl->tpl_vars['clientsdetails']->value['firstname'];?>
!</span>
                                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-user"></i> Client Area
                                </a>
                                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/logout.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a>
                            </div>
                        <?php } else { ?>
                            <div class="widdx-auth-links">
                                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/login.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </a>
                                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/register.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-user-plus"></i> Register
                                </a>
                            </div>
                        <?php }?>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" role="main">
        <!-- Flash Messages -->
        <?php if ($_smarty_tpl->tpl_vars['errormessage']->value) {?>
            <div class="container mt-3">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Error:</strong> <?php echo $_smarty_tpl->tpl_vars['errormessage']->value;?>

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        <?php }?>
        
        <?php if ($_smarty_tpl->tpl_vars['successmessage']->value) {?>
            <div class="container mt-3">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i>
                    <strong>Success:</strong> <?php echo $_smarty_tpl->tpl_vars['successmessage']->value;?>

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        <?php }?>
        
        <?php if ($_smarty_tpl->tpl_vars['infomessage']->value) {?>
            <div class="container mt-3">
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle"></i>
                    <strong>Info:</strong> <?php echo $_smarty_tpl->tpl_vars['infomessage']->value;?>

                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            </div>
        <?php }?>
    </main>

    <!-- Footer -->
    <footer class="widdx-order-footer" role="contentinfo">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <div class="widdx-footer-info">
                        <p>&copy; <?php echo date('Y');?>
 <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
. All rights reserved.</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="widdx-footer-links text-right">
                        <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/contact.php">Contact</a>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/supporttickets.php">Support</a>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/knowledgebase.php">Knowledge Base</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Security and Performance Scripts -->
    <?php echo '<script'; ?>
>
        // CSRF Protection
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            if (window.performance && window.performance.timing) {
                var loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
                console.log('Page load time:', loadTime + 'ms');
            }
        });
    <?php echo '</script'; ?>
>
    
    <?php echo $_smarty_tpl->tpl_vars['footeroutput']->value;?>

</body>
</html>
<?php }
}
