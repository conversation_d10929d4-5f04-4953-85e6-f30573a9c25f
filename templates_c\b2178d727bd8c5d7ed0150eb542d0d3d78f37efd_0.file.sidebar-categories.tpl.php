<?php
/* Smarty version 3.1.48, created on 2025-07-21 02:13:04
  from 'C:\xampp\htdocs\templates\orderforms\widdx_modern\sidebar-categories.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_687d86105f3993_89609359',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'b2178d727bd8c5d7ed0150eb542d0d3d78f37efd' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\orderforms\\widdx_modern\\sidebar-categories.tpl',
      1 => 1753048028,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_687d86105f3993_89609359 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="widdx-sidebar-content">
    <div class="widdx-sidebar-header">
        <h4 class="widdx-sidebar-title">
            <i class="fas fa-list"></i>
            Product Categories
        </h4>
    </div>

    <?php if ($_smarty_tpl->tpl_vars['productGroups']->value) {?>
        <div class="widdx-category-list">
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['productGroups']->value, 'productGroup');
$_smarty_tpl->tpl_vars['productGroup']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['productGroup']->value) {
$_smarty_tpl->tpl_vars['productGroup']->do_else = false;
?>
                <div class="widdx-category-item">
                    <a href="#group-<?php echo $_smarty_tpl->tpl_vars['productGroup']->value->id;?>
" class="widdx-category-link" data-group-id="<?php echo $_smarty_tpl->tpl_vars['productGroup']->value->id;?>
">
                        <div class="widdx-category-icon">
                            <?php if (strpos(mb_strtolower($_smarty_tpl->tpl_vars['productGroup']->value->name, 'UTF-8'),'hosting') !== false) {?>
                                <i class="fas fa-server"></i>
                            <?php } elseif (strpos(mb_strtolower($_smarty_tpl->tpl_vars['productGroup']->value->name, 'UTF-8'),'domain') !== false) {?>
                                <i class="fas fa-globe"></i>
                            <?php } elseif (strpos(mb_strtolower($_smarty_tpl->tpl_vars['productGroup']->value->name, 'UTF-8'),'ssl') !== false) {?>
                                <i class="fas fa-shield-alt"></i>
                            <?php } elseif (strpos(mb_strtolower($_smarty_tpl->tpl_vars['productGroup']->value->name, 'UTF-8'),'email') !== false) {?>
                                <i class="fas fa-envelope"></i>
                            <?php } else { ?>
                                <i class="fas fa-cube"></i>
                            <?php }?>
                        </div>
                        <div class="widdx-category-info">
                            <div class="widdx-category-name"><?php echo $_smarty_tpl->tpl_vars['productGroup']->value->name;?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['productGroup']->value->tagline) {?>
                                <div class="widdx-category-tagline"><?php echo $_smarty_tpl->tpl_vars['productGroup']->value->tagline;?>
</div>
                            <?php }?>
                            <div class="widdx-category-count">
                                <?php echo count($_smarty_tpl->tpl_vars['productGroup']->value->products);?>
 <?php if (count($_smarty_tpl->tpl_vars['productGroup']->value->products) == 1) {?>product<?php } else { ?>products<?php }?>
                            </div>
                        </div>
                        <div class="widdx-category-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </div>
    <?php }?>

    <!-- Quick Actions -->
    <div class="widdx-quick-actions mt-4">
        <div class="widdx-sidebar-header">
            <h5 class="widdx-sidebar-subtitle">
                <i class="fas fa-bolt"></i>
                Quick Actions
            </h5>
        </div>
        
        <div class="widdx-quick-action-list">
            <?php if ($_smarty_tpl->tpl_vars['registerdomainenabled']->value) {?>
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=add&domain=register" class="widdx-quick-action">
                    <i class="fas fa-search"></i>
                    <span>Search Domains</span>
                </a>
            <?php }?>
            
            <?php if ($_smarty_tpl->tpl_vars['transferdomainenabled']->value) {?>
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=add&domain=transfer" class="widdx-quick-action">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Transfer Domain</span>
                </a>
            <?php }?>
            
            <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/contact.php" class="widdx-quick-action">
                <i class="fas fa-comments"></i>
                <span>Contact Sales</span>
            </a>
            
            <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/knowledgebase.php" class="widdx-quick-action">
                <i class="fas fa-question-circle"></i>
                <span>Help Center</span>
            </a>
        </div>
    </div>

    <!-- Trust Badges -->
    <div class="widdx-trust-badges mt-4">
        <div class="widdx-sidebar-header">
            <h5 class="widdx-sidebar-subtitle">
                <i class="fas fa-shield-alt"></i>
                Why Choose Us?
            </h5>
        </div>
        
        <div class="widdx-trust-badge-list">
            <div class="widdx-trust-badge">
                <i class="fas fa-lock text-success"></i>
                <span>Secure Payments</span>
            </div>
            <div class="widdx-trust-badge">
                <i class="fas fa-headset text-primary"></i>
                <span>24/7 Support</span>
            </div>
            <div class="widdx-trust-badge">
                <i class="fas fa-rocket text-warning"></i>
                <span>Instant Setup</span>
            </div>
            <div class="widdx-trust-badge">
                <i class="fas fa-undo text-info"></i>
                <span>Money Back Guarantee</span>
            </div>
        </div>
    </div>

    <!-- Cart Summary (if items in cart) -->
    <?php if ($_smarty_tpl->tpl_vars['cartitems']->value) {?>
        <div class="widdx-cart-summary mt-4">
            <div class="widdx-sidebar-header">
                <h5 class="widdx-sidebar-subtitle">
                    <i class="fas fa-shopping-cart"></i>
                    Your Cart
                </h5>
            </div>
            
            <div class="widdx-cart-items">
                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['cartitems']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?>
                    <div class="widdx-cart-item">
                        <div class="widdx-cart-item-name"><?php echo $_smarty_tpl->tpl_vars['item']->value['productname'];?>
</div>
                        <div class="widdx-cart-item-price"><?php echo $_smarty_tpl->tpl_vars['item']->value['pricingtext'];?>
</div>
                    </div>
                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
            </div>
            
            <div class="widdx-cart-total">
                <div class="widdx-cart-total-label">Total:</div>
                <div class="widdx-cart-total-amount"><?php echo $_smarty_tpl->tpl_vars['carttotal']->value;?>
</div>
            </div>
            
            <div class="widdx-cart-actions">
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=view" class="btn btn-widdx-primary btn-sm btn-block">
                    <i class="fas fa-eye"></i>
                    View Cart
                </a>
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=checkout" class="btn btn-success btn-sm btn-block mt-2">
                    <i class="fas fa-credit-card"></i>
                    Checkout
                </a>
            </div>
        </div>
    <?php }?>

    <!-- Contact Information -->
    <div class="widdx-contact-info mt-4">
        <div class="widdx-sidebar-header">
            <h5 class="widdx-sidebar-subtitle">
                <i class="fas fa-phone"></i>
                Need Help?
            </h5>
        </div>
        
        <div class="widdx-contact-details">
            <div class="widdx-contact-item">
                <i class="fas fa-envelope"></i>
                <a href="mailto:sales@<?php echo mb_strtolower($_smarty_tpl->tpl_vars['companyname']->value, 'UTF-8');?>
.com">sales@<?php echo mb_strtolower($_smarty_tpl->tpl_vars['companyname']->value, 'UTF-8');?>
.com</a>
            </div>
            <div class="widdx-contact-item">
                <i class="fas fa-comments"></i>
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/submitticket.php">Live Chat Support</a>
            </div>
            <div class="widdx-contact-item">
                <i class="fas fa-book"></i>
                <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/knowledgebase.php">Knowledge Base</a>
            </div>
        </div>
    </div>
</div>

<style>
/* Sidebar Specific Styles */
.widdx-sidebar-content {
    background: white;
    border-radius: var(--widdx-border-radius-lg);
    overflow: hidden;
}

.widdx-sidebar-header {
    background: var(--widdx-gradient-primary);
    color: white;
    padding: 1rem;
    margin-bottom: 1rem;
}

.widdx-sidebar-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.widdx-sidebar-subtitle {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.widdx-category-item {
    border-bottom: 1px solid #f8f9fa;
}

.widdx-category-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.widdx-category-link:hover {
    background: #f8f9fa;
    color: var(--widdx-primary);
    text-decoration: none;
}

.widdx-category-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--widdx-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: var(--widdx-primary);
}

.widdx-category-info {
    flex: 1;
}

.widdx-category-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.widdx-category-tagline {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.widdx-category-count {
    font-size: 0.75rem;
    color: #6c757d;
}

.widdx-category-arrow {
    color: #6c757d;
    transition: transform 0.3s ease;
}

.widdx-category-link:hover .widdx-category-arrow {
    transform: translateX(5px);
}

.widdx-quick-action {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #2c3e50;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.3s ease;
}

.widdx-quick-action:hover {
    background: #f8f9fa;
    color: var(--widdx-primary);
    text-decoration: none;
}

.widdx-quick-action i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.widdx-trust-badge {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.widdx-trust-badge i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
}

.widdx-cart-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f8f9fa;
}

.widdx-cart-item-name {
    font-size: 0.875rem;
    font-weight: 500;
}

.widdx-cart-item-price {
    font-size: 0.875rem;
    color: var(--widdx-primary);
    font-weight: 600;
}

.widdx-cart-total {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: #f8f9fa;
    font-weight: 600;
}

.widdx-cart-actions {
    padding: 1rem;
}

.widdx-contact-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.widdx-contact-item i {
    margin-right: 0.75rem;
    width: 20px;
    text-align: center;
    color: var(--widdx-primary);
}

.widdx-contact-item a {
    color: #2c3e50;
    text-decoration: none;
}

.widdx-contact-item a:hover {
    color: var(--widdx-primary);
    text-decoration: none;
}
</style>
<?php }
}
